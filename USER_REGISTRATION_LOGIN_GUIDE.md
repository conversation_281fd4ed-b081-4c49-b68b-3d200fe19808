# RPAL门户网站用户注册登录功能使用指南

## 功能概述

RPAL门户网站已实现完整的用户注册和登录功能，包括邮箱验证码验证、用户注册、用户登录等核心功能。

## 配置要求

### 1. 数据库配置
确保MySQL数据库已启动，并执行初始化脚本：
```sql
-- 执行 src/main/resources/sql/init.sql 文件
```

### 2. Redis配置
确保Redis服务已启动，用于存储验证码：
```bash
# 启动Redis服务
redis-server
```

### 3. 邮件配置
在 `application.yml` 中配置邮件服务：
```yaml
spring:
  mail:
    host: smtp.qq.com  # 邮件服务器地址
    port: 587
    username: <EMAIL>  # 发送邮件的邮箱
    password: your-email-password  # 邮箱授权码
```

**注意：** 
- QQ邮箱需要开启SMTP服务并获取授权码
- 其他邮箱服务商请参考相应配置

## API接口使用说明

### 1. 发送邮箱验证码

**接口：** `POST /api/users/send-verification-code`

**请求体：**
```json
{
  "email": "<EMAIL>"
}
```

**响应：**
```json
{
  "code": 200,
  "message": "验证码发送成功，请查收邮件",
  "data": null,
  "timestamp": "2024-01-01T12:00:00"
}
```

**说明：**
- 验证码为6位数字
- 有效期5分钟
- 同一邮箱60秒内只能发送一次

### 2. 验证邮箱验证码

**接口：** `POST /api/users/verify-code`

**请求体：**
```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

**响应：**
```json
{
  "code": 200,
  "message": "验证码验证成功",
  "data": null,
  "timestamp": "2024-01-01T12:00:00"
}
```

### 3. 用户注册

**接口：** `POST /api/users/register`

**请求体：**
```json
{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "password123",
  "confirmPassword": "password123",
  "verificationCode": "123456",
  "orcid": "0000-0000-0000-0000",
  "researchField": "Computer Science"
}
```

**响应：**
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "id": "RPAL-A1B2C3D4E501",
    "username": "john_doe",
    "email": "<EMAIL>",
    "orcid": "0000-0000-0000-0000",
    "researchField": "Computer Science",
    "avatarUrl": null,
    "createdAt": "2024-01-01T12:00:00",
    "updatedAt": "2024-01-01T12:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

**字段说明：**
- `username`: 用户名，3-50个字符，只能包含字母、数字和下划线
- `email`: 邮箱地址，必须是有效的邮箱格式
- `password`: 密码，6-100个字符
- `confirmPassword`: 确认密码，必须与密码一致
- `verificationCode`: 邮箱验证码，6位数字
- `orcid`: ORCID标识，可选，格式：0000-0000-0000-0000
- `researchField`: 研究领域，可选，最多200个字符

### 4. 用户登录

**接口：** `POST /api/users/login`

**请求体：**
```json
{
  "emailOrUsername": "<EMAIL>",
  "password": "password123"
}
```

**响应：**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "id": "RPAL-A1B2C3D4E501",
    "username": "john_doe",
    "email": "<EMAIL>",
    "orcid": "0000-0000-0000-0000",
    "researchField": "Computer Science",
    "avatarUrl": null,
    "createdAt": "2024-01-01T12:00:00",
    "updatedAt": "2024-01-01T12:00:00"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 5. 检查邮箱可用性

**接口：** `GET /api/users/check-email?email=<EMAIL>`

**响应：**
```json
{
  "code": 200,
  "message": "邮箱检查完成",
  "data": true,  // true表示可用，false表示已被使用
  "timestamp": "2024-01-01T12:00:00"
}
```

### 6. 检查用户名可用性

**接口：** `GET /api/users/check-username?username=john_doe`

**响应：**
```json
{
  "code": 200,
  "message": "用户名检查完成",
  "data": true,  // true表示可用，false表示已被使用
  "timestamp": "2024-01-01T12:00:00"
}
```

## 注册流程示例

### 1. 前端注册流程
```javascript
// 1. 检查邮箱是否可用
const checkEmail = async (email) => {
  const response = await fetch(`/api/users/check-email?email=${email}`);
  const result = await response.json();
  return result.data; // true表示可用
};

// 2. 发送验证码
const sendCode = async (email) => {
  const response = await fetch('/api/users/send-verification-code', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email })
  });
  return await response.json();
};

// 3. 验证验证码
const verifyCode = async (email, code) => {
  const response = await fetch('/api/users/verify-code', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, code })
  });
  return await response.json();
};

// 4. 用户注册
const register = async (userData) => {
  const response = await fetch('/api/users/register', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(userData)
  });
  return await response.json();
};
```

## 错误处理

### 常见错误码
- `400`: 请求参数错误
- `409`: 资源冲突（邮箱或用户名已存在）
- `422`: 验证码错误或已过期
- `429`: 请求过于频繁
- `500`: 服务器内部错误

### 错误响应示例
```json
{
  "code": 409,
  "message": "邮箱已被注册",
  "data": null,
  "timestamp": "2024-01-01T12:00:00",
  "path": "/api/users/register"
}
```

## 安全特性

1. **密码加密**: 使用BCrypt算法加密存储密码
2. **验证码限制**: 同一邮箱60秒内只能发送一次验证码
3. **验证码过期**: 验证码5分钟后自动过期
4. **参数校验**: 完整的请求参数校验
5. **逻辑删除**: 用户数据采用逻辑删除，保证数据安全

## 测试建议

1. 使用Swagger UI进行接口测试：`http://localhost:8081/rpal_portal/swagger-ui.html`
2. 确保邮件服务配置正确，能够正常发送邮件
3. 确保Redis服务正常运行
4. 测试各种边界情况和错误场景

## 扩展功能

后续可以基于现有功能扩展：
- JWT Token认证
- 社交登录（Google、GitHub等）
- 手机号注册登录
- 找回密码功能
- 用户头像上传
- 用户资料完善
