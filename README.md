# RPAL Portal Backend

RPAL门户网站后端服务，基于Spring Boot 3.5.4开发。

## 技术栈

- **框架**: Spring Boot 3.5.4
- **数据库**: MySQL
- **ORM**: MyBatis-Plus 3.5.8
- **缓存**: Redis
- **安全**: Spring Security
- **API文档**: Swagger3 (OpenAPI 3)
- **Java版本**: 17

## 快速开始

### 环境要求

- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 运行项目

1. 克隆项目
```bash
git clone <repository-url>
cd RPAL_portal_backend
```

2. 配置数据库
   - 创建数据库 `rpal`
   - 修改 `src/main/resources/application.yml` 中的数据库连接信息

3. 启动项目
```bash
mvn spring-boot:run
```

或者使用IDE直接运行 `RpalPortalBackendApplication.java`

### API文档

项目集成了Swagger3，启动项目后可以通过以下地址访问API文档：

- **Swagger UI**: http://localhost:8081/rpal_portal/swagger-ui.html
- **OpenAPI JSON**: http://localhost:8081/rpal_portal/v3/api-docs

### 测试接口

项目提供了测试接口来验证系统是否正常运行：

- **Hello World**: `GET /rpal_portal/hello/world`
- **系统状态**: `GET /rpal_portal/hello/status`

## 项目结构

```
src/main/java/com/rpal/rpal_portal_backend/
├── config/                 # 配置类
│   ├── MybatisPlusConfig.java
│   ├── MyMetaObjectHandler.java
│   ├── SecurityConfig.java
│   └── SwaggerConfig.java
├── controller/             # 控制器
│   ├── HelloWorldController.java
│   └── UserController.java
├── entity/                 # 实体类
│   ├── BaseEntity.java
│   ├── User.java
│   ├── Tool.java
│   ├── Category.java
│   ├── News.java
│   ├── Staff.java
│   ├── UserAction.java
│   ├── UserTool.java
│   └── SystemConfig.java
├── common/                 # 通用组件
│   ├── enums/
│   ├── exception/
│   ├── handler/
│   ├── result/
│   └── utils/
├── dto/                    # 数据传输对象
├── mapper/                 # 数据访问层
├── service/                # 业务逻辑层
└── generator/              # 代码生成器
```

## 核心功能

### 用户管理
- ✅ 邮箱验证码发送和验证
- ✅ 用户注册（支持ORCID）
- ✅ 用户登录（邮箱/用户名）
- ✅ 用户信息管理
- ✅ 邮箱和用户名可用性检查

### 工具管理
- 工具分类管理
- 工具信息维护
- 工具使用统计

### 新闻管理
- 新闻发布
- 新闻分类
- 新闻置顶

### 系统管理
- 员工管理
- 系统配置
- 用户行为统计

## API接口

### 用户相关接口
- `POST /api/users/send-verification-code` - 发送邮箱验证码
- `POST /api/users/verify-code` - 验证邮箱验证码
- `POST /api/users/register` - 用户注册
- `POST /api/users/login` - 用户登录
- `GET /api/users/{userId}` - 获取用户信息
- `GET /api/users/check-email` - 检查邮箱是否可用
- `GET /api/users/check-username` - 检查用户名是否可用

## 开发规范

### API设计规范
- 使用RESTful API设计
- 统一的响应格式（Result类）
- 完整的Swagger文档注解

### 异常处理
- 分层异常处理体系
- 全局异常处理器
- 统一错误码定义

### 数据库设计
- 统一的基础实体类
- 逻辑删除支持
- 自动时间戳填充

## 配置说明

### 数据库配置
```yaml
spring:
  datasource:
    url: ********************************
    username: root
    password: 123456
```

### 邮件配置
```yaml
spring:
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-email-password
```

### Redis配置
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
```

### Swagger配置
```yaml
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
```

## 部署

### 打包
```bash
mvn clean package
```

### 运行
```bash
java -jar target/RPAL_portal_backend-0.0.1-SNAPSHOT.jar
```

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
