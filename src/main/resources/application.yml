spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************
    username: root
    password: 123456

  # 邮件配置
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: khkvicpajfhhgcgh
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
    default-encoding: UTF-8

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启SQL日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:

      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
  # mapper文件位置
  mapper-locations: classpath*:/mapper/**/*.xml
  # 实体类别名包路径
  type-aliases-package: com.rpal.rpal_portal_backend.entity

# 服务器配置
server:
  port: 8081
  servlet:
    context-path: /rpal_portal

# Swagger配置
springdoc:
  api-docs:
    # API文档路径
    path: /v3/api-docs
    # 是否启用API文档
    enabled: true
  swagger-ui:
    # Swagger UI路径
    path: /swagger-ui.html
    # 是否启用Swagger UI
    enabled: true
    # 操作排序方式 (alpha: 字母排序, method: HTTP方法排序)
    operations-sorter: alpha
    # 标签排序方式
    tags-sorter: alpha
    # 是否显示请求持续时间
    display-request-duration: true
    # 默认展开深度
    doc-expansion: none
    # 是否显示扩展
    show-extensions: true
    # 是否显示通用扩展
    show-common-extensions: true
  # 分组配置
  group-configs:
    - group: 'default'
      display-name: '默认分组'
      paths-to-match: '/**'
  # 包扫描路径
  packages-to-scan: com.rpal.rpal_portal_backend.controller

# JWT配置
jwt:
  # JWT密钥（生产环境请使用更复杂的密钥）
  secret: rpal-admin-jwt-secret-key-2024-very-long-and-secure-key-for-hmac-sha256-algorithm
  # 访问Token过期时间（小时）
  expiration: 24
  # 刷新Token过期时间（天）
  refresh-expiration: 7

# CORS跨域配置
cors:
  # 允许的源（开发环境，生产环境请修改为具体域名）
  allowed-origins: http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000,http://127.0.0.1:8080,http://localhost:5173,http://127.0.0.1:5173
  # 允许的HTTP方法
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS,PATCH
  # 允许的请求头
  allowed-headers: Authorization,Content-Type,X-Requested-With,Accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers
  # 是否允许携带凭证
  allow-credentials: true
  # 预检请求缓存时间（秒）
  max-age: 3600
