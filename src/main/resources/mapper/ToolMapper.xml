<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rpal.rpal_portal_backend.mapper.ToolMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.rpal.rpal_portal_backend.entity.Tool">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="tool_name" property="toolName" jdbcType="VARCHAR"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="icon_url" property="iconUrl" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="category_id" property="categoryId" jdbcType="BIGINT"/>
        <result column="click_count" property="clickCount" jdbcType="INTEGER"/>
        <result column="last_check_time" property="lastCheckTime" jdbcType="TIMESTAMP"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, tool_name, url, description, icon_url, status, category_id, click_count, last_check_time, created_at, updated_at, deleted
    </sql>

    <!-- 根据分类ID查询工具列表 -->
    <select id="selectByCategoryId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tools
        WHERE category_id = #{categoryId}
        AND status = 'ACTIVE'
        AND deleted = 0
        ORDER BY click_count DESC, created_at DESC
    </select>

    <!-- 根据状态查询工具列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tools
        WHERE status = #{status}
        AND deleted = 0
        ORDER BY click_count DESC, created_at DESC
    </select>

    <!-- 搜索工具 -->
    <select id="searchTools" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tools
        WHERE (tool_name LIKE CONCAT('%', #{keyword}, '%') 
               OR description LIKE CONCAT('%', #{keyword}, '%'))
        AND status = 'ACTIVE'
        AND deleted = 0
        ORDER BY click_count DESC, created_at DESC
    </select>

    <!-- 获取热门工具 -->
    <select id="selectPopularTools" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tools
        WHERE status = 'ACTIVE'
        AND deleted = 0
        ORDER BY click_count DESC, created_at DESC
        LIMIT #{limit}
    </select>

    <!-- 增加工具点击次数 -->
    <update id="incrementClickCount">
        UPDATE tools 
        SET click_count = click_count + 1,
            last_check_time = CURRENT_TIMESTAMP
        WHERE id = #{toolId}
        AND deleted = 0
    </update>

    <!-- 根据用户ID查询收藏的工具列表 -->
    <select id="selectFavoriteToolsByUserId" resultMap="BaseResultMap">
        SELECT
        t.id, t.tool_name, t.url, t.description, t.icon_url, t.status, 
        t.category_id, t.click_count, t.last_check_time, t.created_at, t.updated_at, t.deleted
        FROM tools t
        INNER JOIN user_tools ut ON t.id = ut.tool_id
        WHERE ut.user_id = #{userId}
        AND t.status = 'ACTIVE'
        AND t.deleted = 0
        ORDER BY ut.created_at DESC
    </select>

</mapper>
