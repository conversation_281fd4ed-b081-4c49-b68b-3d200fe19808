<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rpal.rpal_portal_backend.mapper.UserToolMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.rpal.rpal_portal_backend.entity.UserTool">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="tool_id" property="toolId" jdbcType="BIGINT"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, user_id, tool_id, created_at
    </sql>

    <!-- 检查用户是否已收藏某个工具 -->
    <select id="existsByUserIdAndToolId" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM user_tools
        WHERE user_id = #{userId}
        AND tool_id = #{toolId}
    </select>

    <!-- 根据用户ID和工具ID查询收藏记录 -->
    <select id="selectByUserIdAndToolId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_tools
        WHERE user_id = #{userId}
        AND tool_id = #{toolId}
    </select>

    <!-- 根据用户ID查询收藏的工具ID列表 -->
    <select id="selectToolIdsByUserId" resultType="java.lang.Long">
        SELECT tool_id
        FROM user_tools
        WHERE user_id = #{userId}
        ORDER BY created_at DESC
    </select>

    <!-- 根据工具ID查询收藏该工具的用户ID列表 -->
    <select id="selectUserIdsByToolId" resultType="java.lang.String">
        SELECT user_id
        FROM user_tools
        WHERE tool_id = #{toolId}
        ORDER BY created_at DESC
    </select>

    <!-- 统计用户收藏的工具数量 -->
    <select id="countByUserId" resultType="int">
        SELECT COUNT(1)
        FROM user_tools
        WHERE user_id = #{userId}
    </select>

    <!-- 统计工具被收藏的次数 -->
    <select id="countByToolId" resultType="int">
        SELECT COUNT(1)
        FROM user_tools
        WHERE tool_id = #{toolId}
    </select>

    <!-- 删除用户的某个收藏记录 -->
    <delete id="deleteByUserIdAndToolId">
        DELETE FROM user_tools
        WHERE user_id = #{userId}
        AND tool_id = #{toolId}
    </delete>

    <!-- 批量删除用户的收藏记录 -->
    <delete id="deleteBatchByUserIdAndToolIds">
        DELETE FROM user_tools
        WHERE user_id = #{userId}
        AND tool_id IN
        <foreach collection="toolIds" item="toolId" open="(" separator="," close=")">
            #{toolId}
        </foreach>
    </delete>

</mapper>
