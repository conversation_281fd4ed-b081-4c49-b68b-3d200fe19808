package com.rpal.rpal_portal_backend.common.result;

import com.rpal.rpal_portal_backend.common.enums.ResponseCode;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 统一结果返回类
 */
@Data
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 消息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    /**
     * 时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 请求路径
     */
    private String path;

    public Result() {
        this.timestamp = LocalDateTime.now();
    }

    public Result(Integer code, String message) {
        this();
        this.code = code;
        this.message = message;
    }

    public Result(Integer code, String message, T data) {
        this(code, message);
        this.data = data;
    }

    public Result(ResponseCode responseCode) {
        this(responseCode.getCode(), responseCode.getMessage());
    }

    public Result(ResponseCode responseCode, T data) {
        this(responseCode.getCode(), responseCode.getMessage(), data);
    }

    // ========== 成功响应 ==========

    /**
     * 成功响应
     */
    public static <T> Result<T> success() {
        return new Result<>(ResponseCode.SUCCESS);
    }

    /**
     * 成功响应，带数据
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(ResponseCode.SUCCESS, data);
    }

    /**
     * 成功响应，自定义消息
     */
    public static <T> Result<T> success(String message) {
        return new Result<>(ResponseCode.SUCCESS.getCode(), message);
    }

    /**
     * 成功响应，自定义消息和数据
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(ResponseCode.SUCCESS.getCode(), message, data);
    }

    // ========== 失败响应 ==========

    /**
     * 失败响应
     */
    public static <T> Result<T> error() {
        return new Result<>(ResponseCode.INTERNAL_SERVER_ERROR);
    }

    /**
     * 失败响应，自定义消息
     */
    public static <T> Result<T> error(String message) {
        return new Result<>(ResponseCode.INTERNAL_SERVER_ERROR.getCode(), message);
    }

    /**
     * 失败响应，使用响应码枚举
     */
    public static <T> Result<T> error(ResponseCode responseCode) {
        return new Result<>(responseCode);
    }

    /**
     * 失败响应，使用响应码枚举和数据
     */
    public static <T> Result<T> error(ResponseCode responseCode, T data) {
        return new Result<>(responseCode, data);
    }

    /**
     * 失败响应，自定义状态码和消息
     */
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<>(code, message);
    }

    /**
     * 失败响应，自定义状态码、消息和数据
     */
    public static <T> Result<T> error(Integer code, String message, T data) {
        return new Result<>(code, message, data);
    }

    // ========== 判断方法 ==========

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return ResponseCode.SUCCESS.getCode().equals(this.code);
    }

    /**
     * 判断是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }

    /**
     * 设置请求路径
     */
    public Result<T> path(String path) {
        this.path = path;
        return this;
    }
}
