package com.rpal.rpal_portal_backend.common.utils;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 */
@Slf4j
@Component
public class JwtUtils {

    /**
     * JWT密钥
     */
    @Value("${jwt.secret:RPAL_Portal_Backend_JWT_Secret_Key_2024_Very_Long_Secret_Key_For_Security}")
    private String secret;

    /**
     * JWT过期时间（小时）
     */
    @Value("${jwt.expiration:24}")
    private int expiration;

    /**
     * 刷新Token过期时间（天）
     */
    @Value("${jwt.refresh-expiration:7}")
    private int refreshExpiration;

    /**
     * Token前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * Token请求头名称
     */
    public static final String TOKEN_HEADER = "Authorization";

    /**
     * 用户ID声明键
     */
    public static final String CLAIM_USER_ID = "userId";

    /**
     * 用户名声明键
     */
    public static final String CLAIM_USERNAME = "username";

    /**
     * 邮箱声明键
     */
    public static final String CLAIM_EMAIL = "email";

    /**
     * Token类型声明键
     */
    public static final String CLAIM_TOKEN_TYPE = "tokenType";

    /**
     * 访问Token类型
     */
    public static final String TOKEN_TYPE_ACCESS = "access";

    /**
     * 刷新Token类型
     */
    public static final String TOKEN_TYPE_REFRESH = "refresh";

    /**
     * 获取密钥
     */
    private SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(secret.getBytes());
    }

    /**
     * 生成访问Token
     *
     * @param userId   用户ID
     * @param username 用户名
     * @param email    邮箱
     * @return JWT Token
     */
    public String generateAccessToken(String userId, String username, String email) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_USER_ID, userId);
        claims.put(CLAIM_USERNAME, username);
        claims.put(CLAIM_EMAIL, email);
        claims.put(CLAIM_TOKEN_TYPE, TOKEN_TYPE_ACCESS);

        return createToken(claims, userId, expiration * 60); // 转换为分钟
    }

    /**
     * 生成刷新Token
     *
     * @param userId 用户ID
     * @return 刷新Token
     */
    public String generateRefreshToken(String userId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_USER_ID, userId);
        claims.put(CLAIM_TOKEN_TYPE, TOKEN_TYPE_REFRESH);

        return createToken(claims, userId, refreshExpiration * 24 * 60); // 转换为分钟
    }

    /**
     * 创建Token
     *
     * @param claims           声明
     * @param subject          主题（用户ID）
     * @param expirationMinutes 过期时间（分钟）
     * @return JWT Token
     */
    private String createToken(Map<String, Object> claims, String subject, int expirationMinutes) {
        Date now = new Date();
        Date expireDate = new Date(now.getTime() + expirationMinutes * 60 * 1000L);

        return Jwts.builder()
                .claims(claims)
                .subject(subject)
                .issuedAt(now)
                .expiration(expireDate)
                .signWith(getSigningKey())
                .compact();
    }

    /**
     * 从Token中获取用户ID
     *
     * @param token JWT Token
     * @return 用户ID
     */
    public String getUserIdFromToken(String token) {
        return getClaimFromToken(token, CLAIM_USER_ID, String.class);
    }

    /**
     * 从Token中获取用户名
     *
     * @param token JWT Token
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, CLAIM_USERNAME, String.class);
    }

    /**
     * 从Token中获取邮箱
     *
     * @param token JWT Token
     * @return 邮箱
     */
    public String getEmailFromToken(String token) {
        return getClaimFromToken(token, CLAIM_EMAIL, String.class);
    }

    /**
     * 从Token中获取Token类型
     *
     * @param token JWT Token
     * @return Token类型
     */
    public String getTokenTypeFromToken(String token) {
        return getClaimFromToken(token, CLAIM_TOKEN_TYPE, String.class);
    }

    /**
     * 从Token中获取过期时间
     *
     * @param token JWT Token
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        return getClaimsFromToken(token).getExpiration();
    }

    /**
     * 从Token中获取指定声明
     *
     * @param token JWT Token
     * @param key   声明键
     * @param type  声明值类型
     * @return 声明值
     */
    public <T> T getClaimFromToken(String token, String key, Class<T> type) {
        Claims claims = getClaimsFromToken(token);
        return claims.get(key, type);
    }

    /**
     * 从Token中获取所有声明
     *
     * @param token JWT Token
     * @return 声明
     */
    public Claims getClaimsFromToken(String token) {
        try {
            return Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();
        } catch (Exception e) {
            log.error("解析JWT Token失败: {}", e.getMessage());
            throw new JwtException("无效的Token");
        }
    }

    /**
     * 验证Token是否有效
     *
     * @param token JWT Token
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(token);
            return true;
        } catch (ExpiredJwtException e) {
            log.warn("JWT Token已过期: {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
            log.warn("不支持的JWT Token: {}", e.getMessage());
        } catch (MalformedJwtException e) {
            log.warn("JWT Token格式错误: {}", e.getMessage());
        } catch (SecurityException e) {
            log.warn("JWT Token签名验证失败: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.warn("JWT Token参数错误: {}", e.getMessage());
        } catch (Exception e) {
            log.error("JWT Token验证失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 检查Token是否过期
     *
     * @param token JWT Token
     * @return 是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            return expiration.before(new Date());
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 检查是否为访问Token
     *
     * @param token JWT Token
     * @return 是否为访问Token
     */
    public boolean isAccessToken(String token) {
        try {
            String tokenType = getTokenTypeFromToken(token);
            return TOKEN_TYPE_ACCESS.equals(tokenType);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查是否为刷新Token
     *
     * @param token JWT Token
     * @return 是否为刷新Token
     */
    public boolean isRefreshToken(String token) {
        try {
            String tokenType = getTokenTypeFromToken(token);
            return TOKEN_TYPE_REFRESH.equals(tokenType);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 从请求头中提取Token
     *
     * @param authHeader Authorization请求头
     * @return JWT Token（去除前缀）
     */
    public String extractTokenFromHeader(String authHeader) {
        if (authHeader != null && authHeader.startsWith(TOKEN_PREFIX)) {
            return authHeader.substring(TOKEN_PREFIX.length());
        }
        return null;
    }

    /**
     * 获取Token剩余有效时间（分钟）
     *
     * @param token JWT Token
     * @return 剩余有效时间（分钟）
     */
    public long getTokenRemainingTime(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            long remaining = expiration.getTime() - System.currentTimeMillis();
            return Math.max(0, remaining / (60 * 1000)); // 转换为分钟
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 获取Token信息摘要
     *
     * @param token JWT Token
     * @return Token信息
     */
    public String getTokenInfo(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            return String.format("Token[用户ID: %s, 类型: %s, 过期时间: %s]",
                    claims.get(CLAIM_USER_ID),
                    claims.get(CLAIM_TOKEN_TYPE),
                    LocalDateTime.ofInstant(claims.getExpiration().toInstant(), ZoneId.systemDefault()));
        } catch (Exception e) {
            return "无效Token";
        }
    }
}
