package com.rpal.rpal_portal_backend.common.validation;

import com.rpal.rpal_portal_backend.dto.request.UserRegisterRequest;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * 密码匹配验证器
 */
public class PasswordMatchesValidator implements ConstraintValidator<PasswordMatches, UserRegisterRequest> {

    @Override
    public void initialize(PasswordMatches constraintAnnotation) {
        // 初始化方法，可以在这里获取注解参数
    }

    @Override
    public boolean isValid(UserRegisterRequest request, ConstraintValidatorContext context) {
        if (request == null) {
            return true;
        }
        
        String password = request.getPassword();
        String confirmPassword = request.getConfirmPassword();
        
        if (password == null && confirmPassword == null) {
            return true;
        }
        
        if (password == null || confirmPassword == null) {
            return false;
        }
        
        return password.equals(confirmPassword);
    }
}
