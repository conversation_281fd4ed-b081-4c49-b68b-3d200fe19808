package com.rpal.rpal_portal_backend.common.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * 密码匹配验证注解
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = PasswordMatchesValidator.class)
@Documented
public @interface PasswordMatches {
    
    String message() default "两次输入的密码不一致";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}
