package com.rpal.rpal_portal_backend.controller;

import com.rpal.rpal_portal_backend.common.result.Result;
import com.rpal.rpal_portal_backend.common.utils.JwtUtils;
import com.rpal.rpal_portal_backend.dto.response.UserInfoResponse;
import com.rpal.rpal_portal_backend.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * JWT认证测试控制器
 */
@Tag(name = "JWT认证测试", description = "用于测试JWT认证功能的接口")
@RestController
@RequestMapping("/auth-test")
@RequiredArgsConstructor
public class AuthTestController {

    private final JwtUtils jwtUtils;
    private final UserService userService;

    @Operation(summary = "获取当前用户信息", description = "需要JWT认证，获取当前登录用户的详细信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(schema = @Schema(implementation = UserInfoResponse.class))),
            @ApiResponse(responseCode = "401", description = "未授权或Token无效")
    })
    @GetMapping("/current-user")
    public Result<UserInfoResponse> getCurrentUser(HttpServletRequest request) {
        // 从请求头获取Token
        String authHeader = request.getHeader(JwtUtils.TOKEN_HEADER);
        String token = jwtUtils.extractTokenFromHeader(authHeader);
        
        if (token != null && jwtUtils.validateToken(token)) {
            String userId = jwtUtils.getUserIdFromToken(token);
            UserInfoResponse userInfo = userService.getUserInfo(userId);
            return Result.success("获取用户信息成功", userInfo);
        }
        
        return Result.error("Token无效");
    }

    @Operation(summary = "获取Token信息", description = "需要JWT认证，获取当前Token的详细信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "401", description = "未授权或Token无效")
    })
    @GetMapping("/token-info")
    public Result<Object> getTokenInfo(HttpServletRequest request) {
        // 从请求头获取Token
        String authHeader = request.getHeader(JwtUtils.TOKEN_HEADER);
        String token = jwtUtils.extractTokenFromHeader(authHeader);
        
        if (token != null && jwtUtils.validateToken(token)) {
            // 构建Token信息
            Object tokenInfo = new Object() {
                public final String userId = jwtUtils.getUserIdFromToken(token);
                public final String username = jwtUtils.getUsernameFromToken(token);
                public final String email = jwtUtils.getEmailFromToken(token);
                public final String tokenType = jwtUtils.getTokenTypeFromToken(token);
                public final long remainingTime = jwtUtils.getTokenRemainingTime(token);
                public final String tokenSummary = jwtUtils.getTokenInfo(token);
            };
            
            return Result.success("获取Token信息成功", tokenInfo);
        }
        
        return Result.error("Token无效");
    }

    @Operation(summary = "测试认证状态", description = "需要JWT认证，测试Spring Security认证状态")
    @SecurityRequirement(name = "Bearer Authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "认证成功"),
            @ApiResponse(responseCode = "401", description = "未授权")
    })
    @GetMapping("/auth-status")
    public Result<Object> getAuthStatus() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication != null && authentication.isAuthenticated()) {
            Object authInfo = new Object() {
                public final String principal = authentication.getName();
                public final boolean authenticated = authentication.isAuthenticated();
                public final String authorities = authentication.getAuthorities().toString();
                public final String details = authentication.getDetails() != null ? 
                    authentication.getDetails().toString() : "无详情";
            };
            
            return Result.success("认证状态正常", authInfo);
        }
        
        return Result.error("未认证");
    }

    @Operation(summary = "受保护的资源", description = "需要JWT认证才能访问的受保护资源")
    @SecurityRequirement(name = "Bearer Authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "访问成功"),
            @ApiResponse(responseCode = "401", description = "未授权")
    })
    @GetMapping("/protected-resource")
    public Result<String> getProtectedResource() {
        return Result.success("恭喜！您已成功访问受保护的资源。JWT认证工作正常！");
    }
}
