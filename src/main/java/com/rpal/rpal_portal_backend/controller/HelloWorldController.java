package com.rpal.rpal_portal_backend.controller;

import com.rpal.rpal_portal_backend.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "测试接口", description = "用于测试系统是否正常运行的接口")
@RestController
@RequestMapping("/hello")
public class HelloWorldController {

    @Operation(summary = "Hello World", description = "返回Hello World消息，用于测试API是否正常工作")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功返回Hello World消息")
    })
    @GetMapping("/world")
    public Result<String> helloWorld() {
        return Result.success("Hello World!");
    }

    @Operation(summary = "系统状态检查", description = "检查系统运行状态")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "系统运行正常")
    })
    @GetMapping("/status")
    public Result<String> systemStatus() {
        return Result.success("系统运行正常", "RPAL Portal Backend is running!");
    }
}
