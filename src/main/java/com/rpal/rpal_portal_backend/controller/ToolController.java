package com.rpal.rpal_portal_backend.controller;

import com.rpal.rpal_portal_backend.common.result.Result;
import com.rpal.rpal_portal_backend.common.utils.JwtUtils;
import com.rpal.rpal_portal_backend.dto.response.ToolResponse;
import com.rpal.rpal_portal_backend.service.ToolService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工具管理控制器
 */
@Tag(name = "工具管理", description = "工具查询、搜索等相关的API接口")
@RestController
@RequestMapping("/tools")
@RequiredArgsConstructor
public class ToolController {

    private final ToolService toolService;
    private final JwtUtils jwtUtils;

    @Operation(summary = "获取所有活跃工具", description = "获取所有状态为活跃的工具列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(schema = @Schema(implementation = ToolResponse.class)))
    })
    @GetMapping
    public Result<List<ToolResponse>> getAllActiveTools(HttpServletRequest request) {
        String userId = extractUserIdFromRequest(request);
        List<ToolResponse> tools = toolService.getAllActiveTools(userId);
        return Result.success("获取工具列表成功", tools);
    }

    @Operation(summary = "根据ID获取工具详情", description = "根据工具ID获取工具的详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(schema = @Schema(implementation = ToolResponse.class))),
            @ApiResponse(responseCode = "404", description = "工具不存在")
    })
    @GetMapping("/{toolId}")
    public Result<ToolResponse> getToolById(
            @Parameter(description = "工具ID", required = true) @PathVariable Long toolId,
            HttpServletRequest request) {
        String userId = extractUserIdFromRequest(request);
        ToolResponse tool = toolService.getToolById(toolId, userId);
        return Result.success("获取工具详情成功", tool);
    }

    @Operation(summary = "根据分类获取工具", description = "根据分类ID获取该分类下的所有工具")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(schema = @Schema(implementation = ToolResponse.class)))
    })
    @GetMapping("/category/{categoryId}")
    public Result<List<ToolResponse>> getToolsByCategoryId(
            @Parameter(description = "分类ID", required = true) @PathVariable Long categoryId,
            HttpServletRequest request) {
        String userId = extractUserIdFromRequest(request);
        List<ToolResponse> tools = toolService.getToolsByCategoryId(categoryId, userId);
        return Result.success("获取分类工具列表成功", tools);
    }

    @Operation(summary = "搜索工具", description = "根据关键词搜索工具名称和描述")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "搜索成功",
                    content = @Content(schema = @Schema(implementation = ToolResponse.class)))
    })
    @GetMapping("/search")
    public Result<List<ToolResponse>> searchTools(
            @Parameter(description = "搜索关键词", required = true) @RequestParam String keyword,
            HttpServletRequest request) {
        String userId = extractUserIdFromRequest(request);
        List<ToolResponse> tools = toolService.searchTools(keyword, userId);
        return Result.success("搜索工具成功", tools);
    }

    @Operation(summary = "获取热门工具", description = "根据点击次数获取热门工具列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(schema = @Schema(implementation = ToolResponse.class)))
    })
    @GetMapping("/popular")
    public Result<List<ToolResponse>> getPopularTools(
            @Parameter(description = "限制数量，默认10个") @RequestParam(defaultValue = "10") Integer limit,
            HttpServletRequest request) {
        String userId = extractUserIdFromRequest(request);
        List<ToolResponse> tools = toolService.getPopularTools(limit, userId);
        return Result.success("获取热门工具成功", tools);
    }

    @Operation(summary = "增加工具点击次数", description = "记录用户点击工具，增加点击统计（按天去重，每用户每工具每天只计数一次）")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "记录成功"),
            @ApiResponse(responseCode = "404", description = "工具不存在")
    })
    @PostMapping("/{toolId}/click")
    public Result<String> incrementClickCount(
            @Parameter(description = "工具ID", required = true) @PathVariable Long toolId,
            HttpServletRequest request) {
        
        String userId = extractUserIdFromRequest(request);
        
        if (StringUtils.hasText(userId)) {
            // 用户已登录，使用去重逻辑
            boolean counted = toolService.incrementClickCountWithUser(toolId, userId);
            if (counted) {
                return Result.success("点击记录成功");
            } else {
                return Result.success("今日已记录过点击");
            }
        } else {
            // 用户未登录，使用原有逻辑
            toolService.incrementClickCount(toolId);
            return Result.success("点击记录成功");
        }
    }

    /**
     * 从请求中提取用户ID（如果已登录）
     */
    private String extractUserIdFromRequest(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader(JwtUtils.TOKEN_HEADER);
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            
            if (token != null && jwtUtils.validateToken(token) && jwtUtils.isAccessToken(token)) {
                return jwtUtils.getUserIdFromToken(token);
            }
        } catch (Exception e) {
            // 忽略JWT解析错误，返回null表示未登录
        }
        return null;
    }
}
