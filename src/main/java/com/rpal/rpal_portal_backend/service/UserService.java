package com.rpal.rpal_portal_backend.service;

import com.rpal.rpal_portal_backend.dto.request.RefreshTokenRequest;
import com.rpal.rpal_portal_backend.dto.request.UserLoginRequest;
import com.rpal.rpal_portal_backend.dto.request.UserRegisterRequest;
import com.rpal.rpal_portal_backend.dto.response.AuthResponse;
import com.rpal.rpal_portal_backend.dto.response.UserInfoResponse;
import com.rpal.rpal_portal_backend.entity.User;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 发送邮箱验证码
     * @param email 邮箱地址
     */
    void sendVerificationCode(String email);

    /**
     * 验证邮箱验证码
     * @param email 邮箱地址
     * @param code 验证码
     * @return 验证结果
     */
    boolean verifyCode(String email, String code);

    /**
     * 用户注册
     * @param request 注册请求
     * @return 用户信息
     */
    UserInfoResponse register(UserRegisterRequest request);

    /**
     * 用户登录
     * @param request 登录请求
     * @return 认证响应（包含JWT Token）
     */
    AuthResponse login(UserLoginRequest request);

    /**
     * 根据ID获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    UserInfoResponse getUserInfo(String userId);

    /**
     * 根据邮箱查询用户
     * @param email 邮箱地址
     * @return 用户实体
     */
    User findByEmail(String email);

    /**
     * 根据用户名查询用户
     * @param username 用户名
     * @return 用户实体
     */
    User findByUsername(String username);

    /**
     * 根据邮箱或用户名查询用户
     * @param emailOrUsername 邮箱地址或用户名
     * @return 用户实体
     */
    User findByEmailOrUsername(String emailOrUsername);

    /**
     * 检查邮箱是否已存在
     * @param email 邮箱地址
     * @return 是否存在
     */
    boolean isEmailExists(String email);

    /**
     * 检查用户名是否已存在
     * @param username 用户名
     * @return 是否存在
     */
    boolean isUsernameExists(String username);

    /**
     * 检查ORCID是否已存在
     * @param orcid ORCID标识
     * @return 是否存在
     */
    boolean isOrcidExists(String orcid);

    /**
     * 根据ID查询用户
     * @param userId 用户ID
     * @return 用户实体
     */
    User findById(String userId);

    /**
     * 刷新Token
     * @param request 刷新Token请求
     * @return 新的认证响应
     */
    AuthResponse refreshToken(RefreshTokenRequest request);
}
