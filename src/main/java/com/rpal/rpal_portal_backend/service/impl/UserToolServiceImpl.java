package com.rpal.rpal_portal_backend.service.impl;

import com.rpal.rpal_portal_backend.common.exception.BusinessException;
import com.rpal.rpal_portal_backend.common.utils.Assert;
import com.rpal.rpal_portal_backend.dto.response.ToolResponse;
import com.rpal.rpal_portal_backend.entity.Tool;
import com.rpal.rpal_portal_backend.entity.UserTool;
import com.rpal.rpal_portal_backend.mapper.ToolMapper;
import com.rpal.rpal_portal_backend.mapper.UserToolMapper;
import com.rpal.rpal_portal_backend.service.ToolService;
import com.rpal.rpal_portal_backend.service.UserService;
import com.rpal.rpal_portal_backend.service.UserToolService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户工具收藏服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserToolServiceImpl implements UserToolService {

    private final UserToolMapper userToolMapper;
    private final ToolMapper toolMapper;
    private final ToolService toolService;
    private final UserService userService;

    @Override
    @Transactional
    public boolean favoriteTools(String userId, Long toolId) {
        Assert.hasText(userId, "用户ID不能为空");
        Assert.notNull(toolId, "工具ID不能为空");

        // 检查用户是否存在
        if (userService.findById(userId) == null) {
            throw new BusinessException("用户不存在");
        }

        // 检查工具是否存在且活跃
        if (!toolService.isToolActiveAndExists(toolId)) {
            throw new BusinessException("工具不存在或已下线");
        }

        // 检查是否已经收藏
        if (userToolMapper.existsByUserIdAndToolId(userId, toolId)) {
            log.warn("用户已收藏该工具，用户ID: {}, 工具ID: {}", userId, toolId);
            return false;
        }

        // 创建收藏记录
        UserTool userTool = new UserTool();
        userTool.setUserId(userId);
        userTool.setToolId(toolId);
        userTool.setCreatedAt(LocalDateTime.now());

        int inserted = userToolMapper.insert(userTool);
        if (inserted > 0) {
            log.info("用户收藏工具成功，用户ID: {}, 工具ID: {}", userId, toolId);
            return true;
        }

        return false;
    }

    @Override
    @Transactional
    public boolean unfavoriteTools(String userId, Long toolId) {
        Assert.hasText(userId, "用户ID不能为空");
        Assert.notNull(toolId, "工具ID不能为空");

        // 检查是否已收藏
        if (!userToolMapper.existsByUserIdAndToolId(userId, toolId)) {
            log.warn("用户未收藏该工具，用户ID: {}, 工具ID: {}", userId, toolId);
            return false;
        }

        // 删除收藏记录
        int deleted = userToolMapper.deleteByUserIdAndToolId(userId, toolId);
        if (deleted > 0) {
            log.info("用户取消收藏工具成功，用户ID: {}, 工具ID: {}", userId, toolId);
            return true;
        }

        return false;
    }

    @Override
    @Transactional
    public boolean toggleFavorite(String userId, Long toolId) {
        Assert.hasText(userId, "用户ID不能为空");
        Assert.notNull(toolId, "工具ID不能为空");

        if (isFavorited(userId, toolId)) {
            unfavoriteTools(userId, toolId);
            return false; // 取消收藏后返回false
        } else {
            favoriteTools(userId, toolId);
            return true; // 收藏后返回true
        }
    }

    @Override
    public boolean isFavorited(String userId, Long toolId) {
        if (userId == null || toolId == null) {
            return false;
        }
        return userToolMapper.existsByUserIdAndToolId(userId, toolId);
    }

    @Override
    public List<ToolResponse> getUserFavoriteTools(String userId) {
        Assert.hasText(userId, "用户ID不能为空");

        List<Tool> favoriteTools = toolMapper.selectFavoriteToolsByUserId(userId);
        return favoriteTools.stream()
                .map(tool -> toolService.getToolById(tool.getId(), userId))
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> getUserFavoriteToolIds(String userId) {
        Assert.hasText(userId, "用户ID不能为空");
        return userToolMapper.selectToolIdsByUserId(userId);
    }

    @Override
    public int getUserFavoriteCount(String userId) {
        if (userId == null) {
            return 0;
        }
        return userToolMapper.countByUserId(userId);
    }

    @Override
    public int getToolFavoriteCount(Long toolId) {
        if (toolId == null) {
            return 0;
        }
        return userToolMapper.countByToolId(toolId);
    }

    @Override
    @Transactional
    public int batchFavoriteTools(String userId, List<Long> toolIds) {
        Assert.hasText(userId, "用户ID不能为空");
        Assert.notEmpty(toolIds, "工具ID列表不能为空");

        // 检查用户是否存在
        if (userService.findById(userId) == null) {
            throw new BusinessException("用户不存在");
        }

        int successCount = 0;
        List<String> errors = new ArrayList<>();

        for (Long toolId : toolIds) {
            try {
                if (favoriteTools(userId, toolId)) {
                    successCount++;
                }
            } catch (Exception e) {
                errors.add("工具ID " + toolId + ": " + e.getMessage());
                log.warn("批量收藏失败，用户ID: {}, 工具ID: {}, 错误: {}", userId, toolId, e.getMessage());
            }
        }

        log.info("批量收藏完成，用户ID: {}, 成功: {}, 总数: {}", userId, successCount, toolIds.size());

        if (!errors.isEmpty() && successCount == 0) {
            throw new BusinessException("批量收藏失败: " + String.join("; ", errors));
        }

        return successCount;
    }

    @Override
    @Transactional
    public int batchUnfavoriteTools(String userId, List<Long> toolIds) {
        Assert.hasText(userId, "用户ID不能为空");
        Assert.notEmpty(toolIds, "工具ID列表不能为空");

        int deleted = userToolMapper.deleteBatchByUserIdAndToolIds(userId, toolIds);
        log.info("批量取消收藏完成，用户ID: {}, 成功: {}, 总数: {}", userId, deleted, toolIds.size());
        return deleted;
    }

    @Override
    @Transactional
    public int clearUserFavorites(String userId) {
        Assert.hasText(userId, "用户ID不能为空");

        List<Long> favoriteToolIds = getUserFavoriteToolIds(userId);
        if (favoriteToolIds.isEmpty()) {
            return 0;
        }

        int deleted = userToolMapper.deleteBatchByUserIdAndToolIds(userId, favoriteToolIds);
        log.info("清空用户收藏完成，用户ID: {}, 清空数量: {}", userId, deleted);
        return deleted;
    }
}
