package com.rpal.rpal_portal_backend.service.impl;

import com.rpal.rpal_portal_backend.common.exception.BusinessException;
import com.rpal.rpal_portal_backend.common.exception.ValidationException;
import com.rpal.rpal_portal_backend.common.utils.Assert;
import com.rpal.rpal_portal_backend.common.utils.JwtUtils;
import com.rpal.rpal_portal_backend.dto.request.RefreshTokenRequest;
import com.rpal.rpal_portal_backend.dto.request.UserLoginRequest;
import com.rpal.rpal_portal_backend.dto.request.UserRegisterRequest;
import com.rpal.rpal_portal_backend.dto.response.AuthResponse;
import com.rpal.rpal_portal_backend.dto.response.UserInfoResponse;
import com.rpal.rpal_portal_backend.entity.User;
import com.rpal.rpal_portal_backend.generator.UserIdGenerator;
import com.rpal.rpal_portal_backend.mapper.UserMapper;
import com.rpal.rpal_portal_backend.service.EmailService;
import com.rpal.rpal_portal_backend.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final EmailService emailService;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtils jwtUtils;

    @Override
    public void sendVerificationCode(String email) {
        Assert.hasText(email, "邮箱地址不能为空");
        
        // 检查发送频率限制
        if (!emailService.canSendCode(email)) {
            throw new BusinessException("验证码发送过于频繁，请稍后再试");
        }
        
        // 生成验证码
        String code = emailService.generateVerificationCode();
        
        // 存储验证码到Redis
        emailService.storeVerificationCode(email, code);
        
        // 发送邮件
        emailService.sendVerificationCode(email, code);
        
        // 记录发送时间
        emailService.recordSendTime(email);
        
        log.info("验证码发送成功，邮箱: {}", email);
    }

    @Override
    public boolean verifyCode(String email, String code) {
        Assert.hasText(email, "邮箱地址不能为空");
        Assert.hasText(code, "验证码不能为空");
        
        return emailService.verifyCode(email, code);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserInfoResponse register(UserRegisterRequest request) {
        // 参数校验
        validateRegisterRequest(request);
        
        // 验证验证码
        if (!emailService.verifyCode(request.getEmail(), request.getVerificationCode())) {
            throw new ValidationException("验证码错误或已过期");
        }
        
        // 检查邮箱是否已存在
        if (isEmailExists(request.getEmail())) {
            throw new BusinessException("邮箱已被注册");
        }
        
        // 检查用户名是否已存在
        if (isUsernameExists(request.getUsername())) {
            throw new BusinessException("用户名已被使用");
        }
        
        // 检查ORCID是否已存在（如果提供）
        if (StringUtils.hasText(request.getOrcid()) && isOrcidExists(request.getOrcid())) {
            throw new BusinessException("ORCID已被使用");
        }
        
        // 创建用户实体
        User user = new User();
        user.setId(UserIdGenerator.generateUserId());
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        user.setOrcid(request.getOrcid());
        user.setResearchField(request.getResearchField());
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        user.setDeleted(0);
        
        // 保存用户
        int result = userMapper.insert(user);
        if (result <= 0) {
            throw new BusinessException("用户注册失败");
        }
        
        // 删除验证码
        emailService.removeVerificationCode(request.getEmail());
        
        log.info("用户注册成功，用户ID: {}, 邮箱: {}", user.getId(), user.getEmail());
        
        // 返回用户信息
        return convertToUserInfoResponse(user);
    }

    @Override
    public AuthResponse login(UserLoginRequest request) {
        Assert.hasText(request.getEmailOrUsername(), "邮箱地址或用户名不能为空");
        Assert.hasText(request.getPassword(), "密码不能为空");

        // 查询用户
        User user = findByEmailOrUsername(request.getEmailOrUsername());
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPasswordHash())) {
            throw new BusinessException("密码错误");
        }

        // 生成JWT Token
        String accessToken = jwtUtils.generateAccessToken(user.getId(), user.getUsername(), user.getEmail());
        String refreshToken = jwtUtils.generateRefreshToken(user.getId());

        // 计算过期时间（秒）
        long expiresIn = jwtUtils.getTokenRemainingTime(accessToken) * 60; // 转换为秒

        log.info("用户登录成功，用户ID: {}, 邮箱: {}", user.getId(), user.getEmail());

        // 返回认证响应
        UserInfoResponse userInfo = convertToUserInfoResponse(user);
        return AuthResponse.create(accessToken, refreshToken, expiresIn, userInfo);
    }

    @Override
    public UserInfoResponse getUserInfo(String userId) {
        Assert.hasText(userId, "用户ID不能为空");
        
        User user = userMapper.selectById(userId);
        Assert.userExists(user);
        
        return convertToUserInfoResponse(user);
    }

    @Override
    public User findByEmail(String email) {
        return userMapper.selectByEmail(email);
    }

    @Override
    public User findByUsername(String username) {
        return userMapper.selectByUsername(username);
    }

    @Override
    public User findByEmailOrUsername(String emailOrUsername) {
        return userMapper.selectByEmailOrUsername(emailOrUsername);
    }

    @Override
    public boolean isEmailExists(String email) {
        return userMapper.existsByEmail(email);
    }

    @Override
    public boolean isUsernameExists(String username) {
        return userMapper.existsByUsername(username);
    }

    @Override
    public boolean isOrcidExists(String orcid) {
        if (!StringUtils.hasText(orcid)) {
            return false;
        }
        return userMapper.existsByOrcid(orcid);
    }

    @Override
    public User findById(String userId) {
        Assert.hasText(userId, "用户ID不能为空");
        return userMapper.selectById(userId);
    }

    @Override
    public AuthResponse refreshToken(RefreshTokenRequest request) {
        Assert.hasText(request.getRefreshToken(), "刷新Token不能为空");

        String refreshToken = request.getRefreshToken();

        // 验证刷新Token
        if (!jwtUtils.validateToken(refreshToken)) {
            throw new BusinessException("无效的刷新Token");
        }

        // 检查是否为刷新Token
        if (!jwtUtils.isRefreshToken(refreshToken)) {
            throw new BusinessException("Token类型错误");
        }

        // 从Token中获取用户ID
        String userId = jwtUtils.getUserIdFromToken(refreshToken);

        // 验证用户是否存在
        User user = findById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 生成新的Token
        String newAccessToken = jwtUtils.generateAccessToken(user.getId(), user.getUsername(), user.getEmail());
        String newRefreshToken = jwtUtils.generateRefreshToken(user.getId());

        // 计算过期时间（秒）
        long expiresIn = jwtUtils.getTokenRemainingTime(newAccessToken) * 60; // 转换为秒

        log.info("Token刷新成功，用户ID: {}", userId);

        // 返回新的认证响应
        UserInfoResponse userInfo = convertToUserInfoResponse(user);
        return AuthResponse.create(newAccessToken, newRefreshToken, expiresIn, userInfo);
    }

    /**
     * 校验注册请求参数
     */
    private void validateRegisterRequest(UserRegisterRequest request) {
        Assert.hasText(request.getUsername(), "用户名不能为空");
        Assert.hasText(request.getEmail(), "邮箱地址不能为空");
        Assert.hasText(request.getPassword(), "密码不能为空");
        Assert.hasText(request.getConfirmPassword(), "确认密码不能为空");
        Assert.hasText(request.getVerificationCode(), "验证码不能为空");
        
        // 检查密码确认
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new ValidationException("两次输入的密码不一致");
        }
    }

    /**
     * 转换为用户信息响应DTO
     */
    private UserInfoResponse convertToUserInfoResponse(User user) {
        UserInfoResponse response = new UserInfoResponse();
        BeanUtils.copyProperties(user, response);
        return response;
    }
}
