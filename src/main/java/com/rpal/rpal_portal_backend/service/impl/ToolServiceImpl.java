package com.rpal.rpal_portal_backend.service.impl;

import com.rpal.rpal_portal_backend.common.exception.BusinessException;
import com.rpal.rpal_portal_backend.common.utils.Assert;
import com.rpal.rpal_portal_backend.dto.response.ToolResponse;
import com.rpal.rpal_portal_backend.entity.Tool;
import com.rpal.rpal_portal_backend.entity.UserAction;
import com.rpal.rpal_portal_backend.mapper.ToolMapper;
import com.rpal.rpal_portal_backend.mapper.UserActionMapper;
import com.rpal.rpal_portal_backend.mapper.UserToolMapper;
import com.rpal.rpal_portal_backend.service.ToolService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工具服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ToolServiceImpl implements ToolService {

    private final ToolMapper toolMapper;
    private final UserToolMapper userToolMapper;
    private final UserActionMapper userActionMapper;

    @Override
    public ToolResponse getToolById(Long toolId, String userId) {
        Assert.notNull(toolId, "工具ID不能为空");
        
        Tool tool = toolMapper.selectById(toolId);
        if (tool == null || !"ACTIVE".equals(tool.getStatus())) {
            throw new BusinessException("工具不存在或已下线");
        }
        
        return convertToToolResponse(tool, userId);
    }

    @Override
    public List<ToolResponse> getAllActiveTools(String userId) {
        List<Tool> tools = toolMapper.selectByStatus("ACTIVE");
        return tools.stream()
                .map(tool -> convertToToolResponse(tool, userId))
                .collect(Collectors.toList());
    }

    @Override
    public List<ToolResponse> getToolsByCategoryId(Long categoryId, String userId) {
        Assert.notNull(categoryId, "分类ID不能为空");
        
        List<Tool> tools = toolMapper.selectByCategoryId(categoryId);
        return tools.stream()
                .map(tool -> convertToToolResponse(tool, userId))
                .collect(Collectors.toList());
    }

    @Override
    public List<ToolResponse> searchTools(String keyword, String userId) {
        Assert.hasText(keyword, "搜索关键词不能为空");
        
        List<Tool> tools = toolMapper.searchTools(keyword.trim());
        return tools.stream()
                .map(tool -> convertToToolResponse(tool, userId))
                .collect(Collectors.toList());
    }

    @Override
    public List<ToolResponse> getPopularTools(Integer limit, String userId) {
        if (limit == null || limit <= 0) {
            limit = 10; // 默认返回10个热门工具
        }
        
        List<Tool> tools = toolMapper.selectPopularTools(limit);
        return tools.stream()
                .map(tool -> convertToToolResponse(tool, userId))
                .collect(Collectors.toList());
    }

    @Override
    public void incrementClickCount(Long toolId) {
        Assert.notNull(toolId, "工具ID不能为空");
        
        if (!isToolActiveAndExists(toolId)) {
            throw new BusinessException("工具不存在或已下线");
        }
        
        int updated = toolMapper.incrementClickCount(toolId);
        if (updated > 0) {
            log.debug("工具点击次数增加成功，工具ID: {}", toolId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean incrementClickCountWithUser(Long toolId, String userId) {
        Assert.notNull(toolId, "工具ID不能为空");
        
        if (!isToolActiveAndExists(toolId)) {
            throw new BusinessException("工具不存在或已下线");
        }
        
        // 如果没有用户ID，则使用原有逻辑
        if (!StringUtils.hasText(userId)) {
            incrementClickCount(toolId);
            return true;
        }
        
        // 检查今天是否已经点击过
        LocalDate today = LocalDate.now();
        boolean hasClickedToday = userActionMapper.existsClickByUserAndToolAndDate(userId, toolId, today);
        
        if (hasClickedToday) {
            log.debug("用户今日已点击过该工具，不重复计数。用户ID: {}, 工具ID: {}", userId, toolId);
            return false;
        }
        
        // 增加工具点击次数
        int updated = toolMapper.incrementClickCount(toolId);
        if (updated > 0) {
            // 记录用户行为
            UserAction userAction = new UserAction();
            userAction.setUserId(userId);
            userAction.setToolId(toolId);
            userAction.setActionType("CLICK");
            userAction.setActionTime(LocalDateTime.now());
            userAction.setMetadata("{}"); // 可以根据需要添加元数据
            
            userActionMapper.insertUserAction(userAction);
            
            log.debug("工具点击次数增加成功，并记录用户行为。用户ID: {}, 工具ID: {}", userId, toolId);
            return true;
        }
        
        return false;
    }

    @Override
    public Tool findById(Long toolId) {
        Assert.notNull(toolId, "工具ID不能为空");
        return toolMapper.selectById(toolId);
    }

    @Override
    public boolean isToolActiveAndExists(Long toolId) {
        if (toolId == null) {
            return false;
        }
        
        Tool tool = toolMapper.selectById(toolId);
        return tool != null && "ACTIVE".equals(tool.getStatus());
    }

    /**
     * 转换Tool实体为ToolResponse
     */
    private ToolResponse convertToToolResponse(Tool tool, String userId) {
        ToolResponse response = ToolResponse.builder()
                .id(tool.getId())
                .toolName(tool.getToolName())
                .url(tool.getUrl())
                .description(tool.getDescription())
                .iconUrl(tool.getIconUrl())
                .status(tool.getStatus())
                .categoryId(tool.getCategoryId())
                .clickCount(tool.getClickCount())
                .lastCheckTime(tool.getLastCheckTime())
                .createdAt(tool.getCreatedAt())
                .updatedAt(tool.getUpdatedAt())
                .build();

        // 如果提供了用户ID，检查是否已收藏
        if (StringUtils.hasText(userId)) {
            boolean isFavorited = userToolMapper.existsByUserIdAndToolId(userId, tool.getId());
            response.setIsFavorited(isFavorited);
        } else {
            response.setIsFavorited(false);
        }

        // 获取工具被收藏的次数
        int favoriteCount = userToolMapper.countByToolId(tool.getId());
        response.setFavoriteCount(favoriteCount);

        return response;
    }
}
