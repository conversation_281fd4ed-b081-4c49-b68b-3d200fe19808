package com.rpal.rpal_portal_backend.service;

import com.rpal.rpal_portal_backend.dto.response.ToolResponse;
import com.rpal.rpal_portal_backend.entity.Tool;

import java.util.List;

/**
 * 工具服务接口
 */
public interface ToolService {

    /**
     * 根据ID获取工具信息
     * @param toolId 工具ID
     * @param userId 用户ID（用于判断是否已收藏，可为空）
     * @return 工具信息
     */
    ToolResponse getToolById(Long toolId, String userId);

    /**
     * 获取所有活跃工具列表
     * @param userId 用户ID（用于判断是否已收藏，可为空）
     * @return 工具列表
     */
    List<ToolResponse> getAllActiveTools(String userId);

    /**
     * 根据分类ID获取工具列表
     * @param categoryId 分类ID
     * @param userId 用户ID（用于判断是否已收藏，可为空）
     * @return 工具列表
     */
    List<ToolResponse> getToolsByCategoryId(Long categoryId, String userId);

    /**
     * 搜索工具
     * @param keyword 关键词
     * @param userId 用户ID（用于判断是否已收藏，可为空）
     * @return 工具列表
     */
    List<ToolResponse> searchTools(String keyword, String userId);

    /**
     * 获取热门工具
     * @param limit 限制数量
     * @param userId 用户ID（用于判断是否已收藏，可为空）
     * @return 工具列表
     */
    List<ToolResponse> getPopularTools(Integer limit, String userId);

    /**
     * 增加工具点击次数（旧方法，保持兼容性）
     * @param toolId 工具ID
     */
    void incrementClickCount(Long toolId);

    /**
     * 增加工具点击次数（按用户去重，每天每用户每工具只计数一次）
     * @param toolId 工具ID
     * @param userId 用户ID
     * @return 是否成功增加计数（true=已计数，false=今日已计数过）
     */
    boolean incrementClickCountWithUser(Long toolId, String userId);

    /**
     * 根据ID查询工具实体
     * @param toolId 工具ID
     * @return 工具实体
     */
    Tool findById(Long toolId);

    /**
     * 检查工具是否存在且活跃
     * @param toolId 工具ID
     * @return 是否存在且活跃
     */
    boolean isToolActiveAndExists(Long toolId);
}
