package com.rpal.rpal_portal_backend.service;

import com.rpal.rpal_portal_backend.dto.response.ToolResponse;

import java.util.List;

/**
 * 用户工具收藏服务接口
 */
public interface UserToolService {

    /**
     * 收藏工具
     * @param userId 用户ID
     * @param toolId 工具ID
     * @return 是否收藏成功
     */
    boolean favoriteTools(String userId, Long toolId);

    /**
     * 取消收藏工具
     * @param userId 用户ID
     * @param toolId 工具ID
     * @return 是否取消成功
     */
    boolean unfavoriteTools(String userId, Long toolId);

    /**
     * 切换收藏状态（如果已收藏则取消，如果未收藏则收藏）
     * @param userId 用户ID
     * @param toolId 工具ID
     * @return 操作后的收藏状态（true：已收藏，false：未收藏）
     */
    boolean toggleFavorite(String userId, Long toolId);

    /**
     * 检查用户是否已收藏某个工具
     * @param userId 用户ID
     * @param toolId 工具ID
     * @return 是否已收藏
     */
    boolean isFavorited(String userId, Long toolId);

    /**
     * 获取用户收藏的工具列表
     * @param userId 用户ID
     * @return 收藏的工具列表
     */
    List<ToolResponse> getUserFavoriteTools(String userId);

    /**
     * 获取用户收藏的工具ID列表
     * @param userId 用户ID
     * @return 工具ID列表
     */
    List<Long> getUserFavoriteToolIds(String userId);

    /**
     * 统计用户收藏的工具数量
     * @param userId 用户ID
     * @return 收藏数量
     */
    int getUserFavoriteCount(String userId);

    /**
     * 统计工具被收藏的次数
     * @param toolId 工具ID
     * @return 收藏次数
     */
    int getToolFavoriteCount(Long toolId);

    /**
     * 批量收藏工具
     * @param userId 用户ID
     * @param toolIds 工具ID列表
     * @return 成功收藏的数量
     */
    int batchFavoriteTools(String userId, List<Long> toolIds);

    /**
     * 批量取消收藏工具
     * @param userId 用户ID
     * @param toolIds 工具ID列表
     * @return 成功取消收藏的数量
     */
    int batchUnfavoriteTools(String userId, List<Long> toolIds);

    /**
     * 清空用户的所有收藏
     * @param userId 用户ID
     * @return 清空的数量
     */
    int clearUserFavorites(String userId);
}
