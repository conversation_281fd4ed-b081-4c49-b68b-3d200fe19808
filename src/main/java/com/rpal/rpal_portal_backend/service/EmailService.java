package com.rpal.rpal_portal_backend.service;

/**
 * 邮件服务接口
 */
public interface EmailService {

    /**
     * 发送验证码邮件
     * @param email 邮箱地址
     * @param code 验证码
     */
    void sendVerificationCode(String email, String code);

    /**
     * 生成验证码
     * @return 6位数字验证码
     */
    String generateVerificationCode();

    /**
     * 存储验证码到Redis
     * @param email 邮箱地址
     * @param code 验证码
     */
    void storeVerificationCode(String email, String code);

    /**
     * 验证验证码
     * @param email 邮箱地址
     * @param code 验证码
     * @return 是否验证成功
     */
    boolean verifyCode(String email, String code);

    /**
     * 删除验证码
     * @param email 邮箱地址
     */
    void removeVerificationCode(String email);

    /**
     * 检查验证码发送频率限制
     * @param email 邮箱地址
     * @return 是否可以发送
     */
    boolean canSendCode(String email);

    /**
     * 记录验证码发送时间
     * @param email 邮箱地址
     */
    void recordSendTime(String email);
}
