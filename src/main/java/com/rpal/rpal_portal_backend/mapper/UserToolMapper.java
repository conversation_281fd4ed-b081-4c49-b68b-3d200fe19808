package com.rpal.rpal_portal_backend.mapper;

import com.rpal.rpal_portal_backend.entity.UserTool;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户工具关联Mapper接口
 */
@Mapper
public interface UserToolMapper extends BaseMapper<UserTool> {

    /**
     * 检查用户是否已收藏某个工具
     * @param userId 用户ID
     * @param toolId 工具ID
     * @return 是否已收藏
     */
    boolean existsByUserIdAndToolId(@Param("userId") String userId, @Param("toolId") Long toolId);

    /**
     * 根据用户ID和工具ID查询收藏记录
     * @param userId 用户ID
     * @param toolId 工具ID
     * @return 收藏记录
     */
    UserTool selectByUserIdAndToolId(@Param("userId") String userId, @Param("toolId") Long toolId);

    /**
     * 根据用户ID查询收藏的工具ID列表
     * @param userId 用户ID
     * @return 工具ID列表
     */
    List<Long> selectToolIdsByUserId(@Param("userId") String userId);

    /**
     * 根据工具ID查询收藏该工具的用户ID列表
     * @param toolId 工具ID
     * @return 用户ID列表
     */
    List<String> selectUserIdsByToolId(@Param("toolId") Long toolId);

    /**
     * 统计用户收藏的工具数量
     * @param userId 用户ID
     * @return 收藏数量
     */
    int countByUserId(@Param("userId") String userId);

    /**
     * 统计工具被收藏的次数
     * @param toolId 工具ID
     * @return 收藏次数
     */
    int countByToolId(@Param("toolId") Long toolId);

    /**
     * 删除用户的某个收藏记录
     * @param userId 用户ID
     * @param toolId 工具ID
     * @return 影响行数
     */
    int deleteByUserIdAndToolId(@Param("userId") String userId, @Param("toolId") Long toolId);

    /**
     * 批量删除用户的收藏记录
     * @param userId 用户ID
     * @param toolIds 工具ID列表
     * @return 影响行数
     */
    int deleteBatchByUserIdAndToolIds(@Param("userId") String userId, @Param("toolIds") List<Long> toolIds);
}
