package com.rpal.rpal_portal_backend.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 刷新Token请求DTO
 */
@Data
@Schema(description = "刷新Token请求")
public class RefreshTokenRequest {

    @Schema(description = "刷新Token", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", required = true)
    @NotBlank(message = "刷新Token不能为空")
    private String refreshToken;
}
