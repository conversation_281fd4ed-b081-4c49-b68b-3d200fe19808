package com.rpal.rpal_portal_backend.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 用户登录请求DTO
 */
@Data
@Schema(description = "用户登录请求")
public class UserLoginRequest {

    @Schema(description = "邮箱地址或用户名", example = "<EMAIL>", required = true)
    @NotBlank(message = "邮箱地址或用户名不能为空")
    private String emailOrUsername;

    @Schema(description = "密码", example = "password123", required = true)
    @NotBlank(message = "密码不能为空")
    private String password;
}
