package com.rpal.rpal_portal_backend.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 批量收藏操作请求DTO
 */
@Data
@Schema(description = "批量收藏操作请求")
public class BatchFavoriteRequest {

    @Schema(description = "工具ID列表", example = "[1, 2, 3]", required = true)
    @NotEmpty(message = "工具ID列表不能为空")
    private List<Long> toolIds;
}
